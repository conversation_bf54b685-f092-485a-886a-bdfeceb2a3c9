export interface PageContext {
  url: string
  title: string
  selectedText?: string
  selection?: string
  meta?: Record<string, string>
  bodyPreview?: string
  pageContent?: string
  timestamp?: number
  favicon?: string
  clipboard?: string
  clipboardTimestamp?: number
  // Multi-tab context properties
  includeAllTabs?: boolean
  selectedTabIds?: number[]
  // mode?: "this-tab" | "all-tabs" | "multi-tabs"
  tabs?: Array<{
    id: number
    title: string
    url: string
    favicon?: string
    isActive: boolean
  }>
  totalTabs?: number
  selectedCount?: number
  [key: string]: any
}

/** Role values compatible with the server `message.role` column */
export type MessageRole = "user" | "assistant" | "system"

/**
 * Minimal representation of a rich message part as stored in the
 * server `message.parts` JSON column.  We keep the structure flexible
 * but strongly-type the most common case (plain text).
 */
export interface MessagePart {
  /** The type of this part, e.g. `text`, `image`, `code`, ... */
  type: "text" | "image" | "code" | string
  /** Actual payload – for text this is the text content. */
  content: string
  /** Optional language hint for code blocks, etc. */
  lang?: string
}

/**
 * Attachments are stored in the `attachments` JSON column of the server.
 * They can be URLs or inline base64 payloads depending on upload flow.
 */
export interface Attachment {
  id: string
  /** Mime-type, e.g. `image/png` */
  mime: string
  /** Public URL or base64 data URI */
  src: string
  /** Original filename (if any) */
  name?: string
}

export interface ChatMessage {
  /** Primary key – uuid on server but we keep string for flexibility */
  id: string
  role: MessageRole
  /** Plain text representation used by the extension UI */
  content: string
  /** Milliseconds since epoch – will be converted to `createdAt` on server */
  timestamp: number
  /** Parts array to satisfy server schema (derived from `content` when missing) */
  parts?: MessagePart[]
  /** Attachments synced with server `attachments` column */
  attachments?: Attachment[]
  /** Optional citation / knowledge-source info → server `sources` column */
  sources?: SearchSource[]
  /* Browser-only metadata */
  url?: string
}

export interface SearchSource {
  title?: string
  url: string
  snippet?: string
}

export interface Conversation {
  /** Chat id (uuid on server) */
  id: string
  /** Local tab mapping (-1 means merged conversation) */
  tabId: number
  url: string
  title: string
  favicon?: string
  messages: ChatMessage[]
  domain: string
  /** Timestamp of last activity/update */
  lastActive: number
  // Server fields prepared for later sync
  createdAt?: number
  userId?: string
  visibility?: "private" | "public"
}

export interface SmartContextPayload {
  message: string
  chatId: string
  messageId: string

  // Context Management (renamed from contextState to context)
  context: {
    // Primary context (current site)
    primary: SiteContext

    // Recently active contexts (last 2-3 sites in conversation)
    recent: SiteContext[]

    // Explicitly mentioned contexts for this message
    mentioned: SiteContext[]

    // Available cached contexts (user can reference)
    available: SiteContextReference[]
  }

  // Clean conversation history
  conversationHistory: Array<{
    id: string
    role: "user" | "assistant"
    content: string
    timestamp: number
    // Context snapshot at time of message
    contextSnapshot: {
      primaryContextId: string
      mentionedContextIds: string[]
    }
  }>
}

export interface SiteContext {
  id: string // hash of URL for deduplication
  url: string
  title: string
  domain: string
  pageContent: string // markdown content
  selectedText?: string
  favicon?: string
  timestamp: number
  lastAccessed: number
  meta?: Record<string, string>
}

export interface SiteContextReference {
  id: string
  url: string
  title: string
  domain: string
  favicon?: string
  lastAccessed: number
  isAvailable: boolean // true if full content is cached
}
