/**
 * Content Extraction Library - Improved & Simplified
 * 
 * Professional content extraction with multiple strategies.
 * Maintains original logic but with better error handling and performance.
 */

import TurndownService from "turndown"

// Configure turndown for optimal markdown conversion
const turndown = new TurndownService({
  headingStyle: "atx",
  hr: "---",
  bulletListMarker: "-",
  codeBlockStyle: "fenced",
  fence: "```",
  emDelimiter: "*",
  strongDelimiter: "**",
  linkStyle: "inlined"
})

// Remove unwanted elements
turndown.addRule("removeUnwanted", {
  filter: ["script", "style", "nav", "header", "footer", "aside", "noscript"],
  replacement: () => ""
})

// Clean up excessive whitespace
turndown.addRule("cleanWhitespace", {
  filter: (node) => node.nodeType === 3, // Text nodes
  replacement: (content) => content.replace(/\s+/g, " ").trim()
})

/**
 * Main content extraction function
 * Uses multiple strategies for best results across different sites
 */
export function extractPageContent(): string {
  console.log("[ContentExtraction] Starting extraction process...")
  console.log("[ContentExtraction] Page state:", {
    readyState: document.readyState,
    bodyExists: !!document.body,
    bodyChildren: document.body?.children.length || 0,
    textLength: document.body?.textContent?.length || 0,
    url: window.location.href
  })

  try {
    // Strategy 1: Simple text extraction (fastest, most reliable)
    const simpleText = extractSimpleText()
    if (simpleText && simpleText.length > 100) {
      console.log("[ContentExtraction] Using simple text extraction:", simpleText.length, "chars")
      return simpleText
    }

    // Strategy 2: Semantic content extraction
    const semanticContent = extractSemanticContent()
    if (semanticContent && semanticContent.length > 100) {
      console.log("[ContentExtraction] Using semantic extraction:", semanticContent.length, "chars")
      return turndown.turndown(semanticContent)
    }

    // Strategy 3: Fallback to full body
    const bodyContent = extractBodyContent()
    console.log("[ContentExtraction] Using body extraction:", bodyContent.length, "chars")
    return bodyContent

  } catch (error) {
    console.error("[ContentExtraction] Extraction failed:", error)
    return document.body?.innerText || "Content extraction failed"
  }
}

/**
 * Strategy 1: Simple text extraction using innerText
 */
function extractSimpleText(): string {
  try {
    const text = document.body?.innerText || ""
    
    // Clean up the text
    return text
      .replace(/[\r\n]+/g, "\n") // Normalize line breaks
      .replace(/[ \t]+/g, " ")   // Normalize spaces
      .replace(/\n{3,}/g, "\n\n") // Limit consecutive line breaks
      .trim()
      
  } catch (error) {
    console.warn("[ContentExtraction] Simple text extraction failed:", error)
    return ""
  }
}

/**
 * Strategy 2: Semantic content extraction
 * Targets main content areas while avoiding navigation, ads, etc.
 */
function extractSemanticContent(): string {
  try {
    // Common content selectors (in order of preference)
    const contentSelectors = [
      'main',
      'article', 
      '[role="main"]',
      '.main-content',
      '.content',
      '.post-content',
      '.entry-content',
      '#content',
      '#main',
      '.container .content'
    ]

    // Try each selector
    for (const selector of contentSelectors) {
      const element = document.querySelector(selector)
      if (element && element.textContent && element.textContent.length > 200) {
        console.log(`[ContentExtraction] Found content with selector: ${selector}`)
        return cleanHtmlForExtraction(element)
      }
    }

    return ""
    
  } catch (error) {
    console.warn("[ContentExtraction] Semantic extraction failed:", error)
    return ""
  }
}

/**
 * Strategy 3: Full body content extraction
 */
function extractBodyContent(): string {
  try {
    if (!document.body) return ""
    
    const cleanedBody = cleanHtmlForExtraction(document.body)
    return turndown.turndown(cleanedBody)
    
  } catch (error) {
    console.warn("[ContentExtraction] Body extraction failed:", error)
    return document.body?.innerText || ""
  }
}

/**
 * Clean HTML element for extraction
 */
function cleanHtmlForExtraction(element: Element): string {
  try {
    // Clone to avoid modifying original
    const clone = element.cloneNode(true) as Element
    
    // Remove unwanted elements
    const unwantedSelectors = [
      'script', 'style', 'noscript',
      'nav', 'header', 'footer', 'aside',
      '.nav', '.navbar', '.navigation', 
      '.menu', '.sidebar', '.ads', '.advertisement',
      '.popup', '.modal', '.overlay',
      '.social-share', '.comments', '.related'
    ]
    
    unwantedSelectors.forEach(selector => {
      clone.querySelectorAll(selector).forEach(el => el.remove())
    })
    
    // Return cleaned HTML
    return clone.innerHTML
    
  } catch (error) {
    console.warn("[ContentExtraction] HTML cleaning failed:", error)
    return element.innerHTML
  }
}

/**
 * Debug extraction - for testing different methods
 */
export function debugExtraction(method: string): string {
  console.log(`[ContentExtraction] Debug extraction: ${method}`)
  
  switch (method) {
    case "simple":
      return extractSimpleText()
      
    case "semantic": 
      const semantic = extractSemanticContent()
      return semantic ? turndown.turndown(semantic) : ""
      
    case "body":
      return extractBodyContent()
      
    case "raw":
      return document.body?.innerHTML || ""
      
    default:
      return extractPageContent()
  }
}

// Legacy compatibility - keep the same function name
export const getPageContentAsMarkdown = extractPageContent