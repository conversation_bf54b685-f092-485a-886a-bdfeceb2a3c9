export const MESSAGE_TYPES = {
  EXTRACT_CONTEXT: "EXTRACT_CONTEXT",
  DEBUG_EXTRACT_CONTENT: "DEBUG_EXTRACT_CONTENT",

  CTX_UPDATE: "CTX_UPDATE", // Sending updated context to sidepanel

  // From background to content-script
  CTX_UPDATE_REQUEST: "CTX_UPDATE_REQUEST",
  STM_SAVED: "STM_SAVED", // Short-term memory save confirmation

  // Generic control messages
  PING: "PING",
  OPEN_SIDEPANEL: "OPEN_SIDEPANEL",
  TOGGLE_SIDEPANEL: "TOGGLE_SIDEPANEL",
  OPTIMUS_AUTH_UPDATE: "OPTIMUS_AUTH_UPDATE"
} as const

// The user-facing prompt that shows up in the sidepanel chat
export const AI_PROMPTS = {
  EXPLAIN: (selection: string) =>
    `Gi<PERSON>i thích giúp tôi đoạn này thật ngắn gọn và dễ hiểu: "${selection}"`
} as const

// The different types of AI actions that can be triggered from the floating buttons
export const AI_ACTION_TYPES = {
  EXPLAIN: "explain", // The code uses 'explain'
  ADD_STM: "add_stm"
} as const
