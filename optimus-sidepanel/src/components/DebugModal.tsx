import { X } from "lucide-react"
import { useEffect, useState } from "react"

import { MESSAGE_TYPES } from "~constants"

interface DebugModalProps {
  isOpen: boolean
  onClose: () => void
  context: any
}

export const DebugModal = ({ isOpen, onClose, context }: DebugModalProps) => {
  const [extractedContent, setExtractedContent] = useState<string>("")
  const [isExtracting, setIsExtracting] = useState(false)
  const [extractionSteps, setExtractionSteps] = useState<string[]>([])
  const [rawHtml, setRawHtml] = useState<string>("")

  if (!isOpen) return null

  const addStep = (step: string) => {
    setExtractionSteps((prev) => [
      ...prev,
      `${new Date().toLocaleTimeString()}: ${step}`
    ])
  }

  const testExtraction = async () => {
    setIsExtracting(true)
    setExtractedContent("")
    setExtractionSteps([])

    try {
      addStep("Starting extraction test...")

      // Test 1: Simple document.body.innerText
      addStep("Test 1: Getting document.body.innerText")
      const response1 = await chrome.runtime.sendMessage({
        type: MESSAGE_TYPES.DEBUG_EXTRACT_CONTENT,
        method: "innerText"
      })
      addStep(`Result 1: ${response1?.content?.length || 0} chars`)
      addStep(`Preview: ${response1?.content?.substring(0, 100) || "empty"}`)

      // Test 2: Our extraction function
      addStep("Test 2: Using our extraction function")
      const response2 = await chrome.runtime.sendMessage({
        type: MESSAGE_TYPES.DEBUG_EXTRACT_CONTENT,
        method: "extractPageText"
      })
      addStep(`Result 2: ${response2?.content?.length || 0} chars`)
      addStep(`Preview: ${response2?.content?.substring(0, 100) || "empty"}`)

      // Test 3: Simple extraction
      addStep("Test 3: Using simple extraction")
      const response3 = await chrome.runtime.sendMessage({
        type: MESSAGE_TYPES.DEBUG_EXTRACT_CONTENT,
        method: "simple"
      })
      addStep(`Result 3: ${response3?.content?.length || 0} chars`)
      addStep(`Preview: ${response3?.content?.substring(0, 100) || "empty"}`)

      // Test 4: Get raw HTML
      addStep("Test 4: Getting raw HTML")
      const response4 = await chrome.runtime.sendMessage({
        type: MESSAGE_TYPES.DEBUG_EXTRACT_CONTENT,
        method: "rawHtml"
      })
      addStep(`Result 4: ${response4?.content?.length || 0} chars HTML`)

      // Set the best result - prefer simple extraction
      const bestContent =
        response3?.content || response2?.content || response1?.content || ""
      setExtractedContent(bestContent)

      addStep("Extraction complete!")
    } catch (error) {
      addStep(`Error: ${error}`)
    } finally {
      setIsExtracting(false)
    }
  }
  const getRawHtml = async () => {
    const response = await chrome.runtime.sendMessage({
      type: "GET_RAW_HTML"
    })

    console.log("response", response)
    setRawHtml(response?.content || "")
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[100] flex items-center justify-center p-4">
      <div className="bg-zinc-900 rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden border border-white/10">
        <div className="flex items-center justify-between p-4 border-b border-white/10">
          <h2 className="text-lg font-semibold text-white">
            Content Extraction Debug
          </h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-white/10 rounded-lg transition-colors">
            <X size={20} />
          </button>
        </div>

        <div className="p-4 space-y-4 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Extraction Steps */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-gray-300">
              Extraction Steps
            </h3>
            <div className="bg-black/30 rounded-lg p-3 text-xs font-mono space-y-1 max-h-40 overflow-y-auto">
              {rawHtml}
            </div>
          </div>

          {/* Extracted Content */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-gray-300">
              Extracted Content
            </h3>
            <div className="bg-black/30 rounded-lg p-3 text-xs font-mono max-h-60 overflow-y-auto">
              {extractedContent ? (
                <pre className="whitespace-pre-wrap text-blue-400">
                  {extractedContent}
                </pre>
              ) : (
                <div className="text-gray-500">No content extracted yet.</div>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-2">
            <button
              onClick={getRawHtml}
              className="btn-primary px-4 py-2 rounded-lg text-sm disabled:opacity-50">
              Get Raw HTML
            </button>
            <button
              onClick={testExtraction}
              className="btn-primary px-4 py-2 rounded-lg text-sm disabled:opacity-50">
              Test Extraction
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
