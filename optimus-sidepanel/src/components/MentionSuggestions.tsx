import React, { useEffect, useRef, useState } from "react"

import type { SiteContextReference } from "~types"
import { SmartContextManager } from "~utils/context-service"

interface MentionSuggestionsProps {
  query: string
  onSelect: (context: SiteContextReference) => void
  onClose: () => void
  inputRect?: DOMRect
  isVisible: boolean
}

export const MentionSuggestions: React.FC<MentionSuggestionsProps> = ({
  query,
  onSelect,
  onClose,
  inputRect,
  isVisible
}) => {
  const [suggestions, setSuggestions] = useState<SiteContextReference[]>([])
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [isLoading, setIsLoading] = useState(false)
  const suggestionRefs = useRef<(HTMLDivElement | null)[]>([])

  // Load suggestions based on query
  useEffect(() => {
    if (!isVisible) {
      setSuggestions([])
      return
    }

    if (!query) {
      setSuggestions([])
      return
    }

    const loadSuggestions = async () => {
      setIsLoading(true)
      console.log("[MentionSuggestions] Loading suggestions for query:", query)

      try {
        // Ensure SmartContextManager is initialized
        await SmartContextManager.initialize()

        const results =
          query.length > 0
            ? await SmartContextManager.searchContexts(query)
            : await SmartContextManager.getAvailableContexts()

        console.log("[MentionSuggestions] Search results:", {
          query,
          queryLength: query.length,
          resultsCount: results.length,
          results: results.map((r) => ({ domain: r.domain, title: r.title }))
        })

        // Limit to 5 suggestions
        setSuggestions(results.slice(0, 5))
        setSelectedIndex(0)
      } catch (error) {
        console.error(
          "[MentionSuggestions] Failed to load context suggestions:",
          error
        )
        setSuggestions([])
      } finally {
        setIsLoading(false)
      }
    }

    loadSuggestions()
  }, [query, isVisible])

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isVisible || suggestions.length === 0) return

      switch (e.key) {
        case "ArrowUp":
          e.preventDefault()
          setSelectedIndex((prev) =>
            prev > 0 ? prev - 1 : suggestions.length - 1
          )
          break
        case "ArrowDown":
          e.preventDefault()
          setSelectedIndex((prev) =>
            prev < suggestions.length - 1 ? prev + 1 : 0
          )
          break
        case "Enter":
          e.preventDefault()
          if (suggestions[selectedIndex]) {
            onSelect(suggestions[selectedIndex])
          }
          break
        case "Escape":
          e.preventDefault()
          onClose()
          break
      }
    }

    if (isVisible) {
      document.addEventListener("keydown", handleKeyDown)
      return () => document.removeEventListener("keydown", handleKeyDown)
    }
  }, [isVisible, suggestions, selectedIndex, onSelect, onClose])

  // Scroll to selected item
  useEffect(() => {
    const selectedRef = suggestionRefs.current[selectedIndex]
    if (selectedRef) {
      selectedRef.scrollIntoView({ block: "nearest" })
    }
  }, [selectedIndex])

  if (!isVisible || (suggestions.length === 0 && !isLoading)) {
    return null
  }

  // Calculate position relative to input
  const style: React.CSSProperties = inputRect
    ? {
        position: "fixed",
        top: inputRect.top - 200, // Position above input
        left: inputRect.left,
        width: Math.max(inputRect.width, 300),
        zIndex: 1000
      }
    : {
        position: "absolute",
        bottom: "100%",
        left: 0,
        right: 0,
        zIndex: 1000
      }

  const formatLastAccessed = (timestamp: number): string => {
    const now = Date.now()
    const diff = now - timestamp
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(diff / 3600000)
    const days = Math.floor(diff / 86400000)

    if (minutes < 1) return "just now"
    if (minutes < 60) return `${minutes}m ago`
    if (hours < 24) return `${hours}h ago`
    return `${days}d ago`
  }

  return (
    <div
      style={style}
      className="bg-gray-800 border border-gray-600 rounded-lg shadow-lg max-h-48 overflow-y-auto">
      <div className="p-2 text-xs text-gray-400 border-b border-gray-600">
        {isLoading
          ? "Loading contexts..."
          : `@mention contexts (${suggestions.length})`}
      </div>

      {isLoading ? (
        <div className="p-4 text-center text-gray-400">
          <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full mx-auto"></div>
        </div>
      ) : (
        <div className="py-1">
          {suggestions.map((context, index) => (
            <div
              key={context.id}
              ref={(el) => (suggestionRefs.current[index] = el)}
              className={`px-3 py-2 cursor-pointer transition-colors ${
                index === selectedIndex
                  ? "bg-blue-600 text-white"
                  : "text-gray-200 hover:bg-gray-700"
              }`}
              onClick={() => onSelect(context)}>
              <div className="flex items-center space-x-2">
                {context.favicon && (
                  <img
                    src={context.favicon}
                    alt=""
                    className="w-4 h-4 rounded-sm"
                    onError={(e) => {
                      ;(e.target as HTMLImageElement).style.display = "none"
                    }}
                  />
                )}
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium truncate">
                    {context.title}
                  </div>
                  <div className="text-xs opacity-70 truncate">
                    {context.domain}
                  </div>
                </div>
                <div className="text-xs opacity-50">
                  {formatLastAccessed(context.lastAccessed)}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {!isLoading && suggestions.length === 0 && (
        <div className="p-4 text-center text-gray-500 text-sm">
          No contexts found for &quot;{query}&quot;
        </div>
      )}
    </div>
  )
}
