/**
 * Context Message Handler
 *
 * Handles page context requests, caching, and updates.
 * Supports both single tab and multi-tab context extraction.
 */

import type { PlasmoMessaging } from "@plasmohq/messaging"
import { sendToContentScript } from "@plasmohq/messaging"

import { getCachedContext, setCachedContext } from "~background"
import { MESSAGE_TYPES } from "~constants"
import type { PageContext } from "~types"

interface ContextRequest {
  tabId?: number
  forceRefresh?: boolean
}

interface ContextResponse {
  context: PageContext | null
  cached: boolean
}

const handler: PlasmoMessaging.MessageHandler<
  ContextRequest,
  ContextResponse
> = async (req, res) => {
  const { tabId: requestedTabId, forceRefresh = false } = req.body || {}
  const tabId = requestedTabId || req.sender?.tab?.id

  if (!tabId) {
    console.warn("[Context] No tab ID available")
    res.send({ context: null, cached: false })
    return
  }

  console.log(
    `[Context] Handling request for tab ${tabId}, forceRefresh: ${forceRefresh}`
  )

  try {
    // Check cache first (unless forcing refresh)
    if (!forceRefresh) {
      const cached = getCachedContext(tabId)
      if (cached) {
        console.log(`[Context] Returning cached context for tab ${tabId}`)
        res.send({ context: cached, cached: true })
        return
      }
    }

    // Extract fresh context from content script
    const context = await extractFreshContext(tabId)

    // Cache the result
    if (context) {
      setCachedContext(tabId, context)
    }

    res.send({ context, cached: false })
  } catch (error) {
    console.error(`[Context] Error handling request:`, error)
    res.send({ context: null, cached: false })
  }
}

/**
 * Extract fresh context from content script
 */
async function extractFreshContext(tabId: number): Promise<PageContext | null> {
  try {
    // Request context from content script
    const response = await sendToContentScript({
      name: MESSAGE_TYPES.EXTRACT_CONTEXT,
      body: {
        tabId
      }
    })

    return response?.context || null
  } catch (error) {
    console.error(`[Context] Failed to extract from tab ${tabId}:`, error)
    return null
  }
}

export default handler
