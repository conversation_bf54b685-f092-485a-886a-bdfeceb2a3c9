/**
 * Optimus Content Script
 *
 * Simplified content script using Plasmo patterns.
 * Handles page content extraction and context management.
 */

import type { PlasmoCSConfig } from "plasmo"

import { MESSAGE_TYPES } from "~constants"
import type { PageContext } from "~types"
import { debugExtraction, extractPageContent } from "~utils/content-extraction"

export const config: PlasmoCSConfig = {
  matches: ["<all_urls>"],
  run_at: "document_idle",
  all_frames: false
}

// Prevent double initialization
if (!window.__optimusContentInit) {
  window.__optimusContentInit = true
  initialize()
}

/**
 * Initialize content script
 */
function initialize() {
  console.log(
    "🚀 Optimus content script initializing on:",
    window.location.href
  )

  // Setup message listeners
  setupMessageListeners()

  // Setup navigation listeners for SPA
  setupNavigationListeners()

  console.log("✅ Optimus content script ready")
}

/**
 * Setup Chrome runtime message listeners
 */
function setupMessageListeners() {
  chrome.runtime.onMessage.addListener(
    async (message, sender, sendResponse) => {
      console.log("[Content] Received message:", message.type)

      switch (message.type) {
        case MESSAGE_TYPES.EXTRACT_CONTEXT:
          await handleContextExtraction(sendResponse)
          return true

        case MESSAGE_TYPES.DEBUG_EXTRACT_CONTENT:
          await handleDebugExtraction(message.method, sendResponse)
          return true

        default:
          console.log("[Content] Unknown message type:", message.type)
          sendResponse({ success: false, error: "Unknown message type" })
          return false
      }
    }
  )
}

/**
 * Handle context extraction request
 */
async function handleContextExtraction(sendResponse: (response: any) => void) {
  try {
    console.log("[Content] Extracting page context...")

    // Wait for page to be fully loaded
    await ensurePageReady()

    const context: PageContext = {
      url: window.location.href,
      title: document.title,
      favicon: getFaviconUrl(),
      pageContent: extractPageContent(),
      selectedText: getSelectedText(),
      timestamp: Date.now()
    }

    console.log(
      `[Content] Context extracted: ${context.pageContent?.length || 0} chars`
    )
    sendResponse({ success: true, context })
  } catch (error) {
    console.error("[Content] Context extraction failed:", error)
    sendResponse({ success: false, error: error.message, context: null })
  }
}

/**
 * Handle debug extraction request
 */
async function handleDebugExtraction(
  method: string,
  sendResponse: (response: any) => void
) {
  try {
    console.log(`[Content] Debug extraction: ${method}`)

    await ensurePageReady()

    let content = ""

    switch (method) {
      case "innerText":
        content = document.body?.innerText || ""
        break

      case "markdown":
        content = debugExtraction("body")
        break

      case "html":
        content = document.body?.innerHTML || ""
        break

      case "simple":
        content = document.body?.textContent || ""
        break

      default:
        throw new Error(`Unknown extraction method: ${method}`)
    }

    console.log(`[Content] Debug ${method}: ${content.length} chars`)
    sendResponse({ success: true, content, method })
  } catch (error) {
    console.error(`[Content] Debug extraction failed:`, error)
    sendResponse({ success: false, error: error.message, content: "" })
  }
}

async function ensurePageReady(): Promise<void> {
  // Wait for DOM to be complete
  if (document.readyState !== "complete") {
    await new Promise((resolve) => {
      window.addEventListener("load", resolve, { once: true })
      setTimeout(resolve, 3000) // Timeout after 3 seconds
    })
  }

  // Additional wait for dynamic content
  await new Promise((resolve) => setTimeout(resolve, 500))
}

/**
 * Get selected text from page
 */
function getSelectedText(): string | null {
  const selection = window.getSelection()
  const text = selection?.toString().trim()
  return text && text.length > 0 ? text : null
}

function getFaviconUrl(): string | null {
  const favicon = document.querySelector('link[rel*="icon"]') as HTMLLinkElement
  if (favicon?.href) {
    return favicon.href
  }

  // Fallback to default favicon
  return `${window.location.origin}/favicon.ico`
}

/**
 * Setup navigation listeners for SPA support
 */
function setupNavigationListeners() {
  // Listen for navigation changes in SPAs
  const originalPushState = history.pushState
  const originalReplaceState = history.replaceState

  history.pushState = function (...args) {
    originalPushState.apply(this, args)
    handleNavigation()
  }

  history.replaceState = function (...args) {
    originalReplaceState.apply(this, args)
    handleNavigation()
  }

  window.addEventListener("popstate", handleNavigation)
  window.addEventListener("hashchange", handleNavigation)
}

/**
 * Handle navigation events
 */
function handleNavigation() {
  console.log("[Content] Navigation detected:", window.location.href)
  // Could notify background of URL change here if needed
}

// Global type augmentation
declare global {
  interface Window {
    __optimusContentInit?: boolean
  }
}
